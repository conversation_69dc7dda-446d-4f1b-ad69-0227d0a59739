# SnapCRE Properties Shortcode Enhancement

## Overview
Enhanced the `[snapcre_properties]` shortcode to support all available filter parameters from the properties filter system.

## Changes Made

### 1. Backend Filter Enhancement
**File:** `wp-content/plugins/fcre-properties/admin/class-fcre-properties-admin.php`
- Added year built filter support (`min-year`, `max-year`) to `fcre_filter_properties()` function
- Improved range filter logic to handle single value cases for both building size and year built filters
- Added proper comparison operators (`>=`, `<=`) for single value range filters

### 2. Frontend Multiselect Enhancement
**File:** `wp-content/plugins/fcre-properties/includes/fcre-functions.php`
- Enhanced `fcre_frontend_multiselect()` function to support pre-selected values
- Added `selected_values` parameter to accept array of pre-selected option IDs
- Updated checkbox generation to mark pre-selected options as checked

### 3. Shortcode Function Enhancement
**File:** `wp-content/plugins/fcre-properties/public/class-fcre-properties-public.php`
- Enhanced `snapcre_filters()` function to accept all filter parameters
- Added support for: `property_types`, `transaction_types`, `property_status`, `address`, `min_sf`, `max_sf`, `min_year`, `max_year`
- Pass all parameters to the filter template

### 4. Filter Template Enhancement
**File:** `wp-content/plugins/fcre-properties/public/template-parts/filters/fcre-filters.php`
- Added parameter parsing logic to handle shortcode attributes
- Pre-populate form fields with shortcode parameter values
- Updated multiselect calls to include selected values
- Added value attributes to text and number inputs

### 5. Documentation Update
**File:** `wp-content/plugins/fcre-properties/includes/class-fcre-properties.php`
- Updated shortcode comment to show all available parameters

## Supported Parameters

### Multiselect Parameters
- `property_types` - Comma-separated list of property type IDs (e.g., "1,2,3")
- `transaction_types` - Comma-separated list of transaction type IDs (e.g., "1,2")
- `property_status` - Comma-separated list of property status IDs (e.g., "1,2")

### Text Parameters
- `address` - Address search string (e.g., "New York", "Downtown")

### Numeric Range Parameters
- `min_sf` - Minimum building size in square feet
- `max_sf` - Maximum building size in square feet
- `min_year` - Minimum year built
- `max_year` - Maximum year built

## Usage Examples

### Basic Usage
```
[snapcre_properties]
```

### Property Types Only
```
[snapcre_properties property_types="1,2,3"]
```

### Multiple Filters
```
[snapcre_properties property_types="1,2" transaction_types="1" min_sf="1000" max_sf="5000"]
```

### All Parameters
```
[snapcre_properties property_types="1,2" transaction_types="1" property_status="2" address="Downtown" min_sf="1500" max_sf="4000" min_year="2005" max_year="2018"]
```

## Technical Details

### Parameter Processing
1. Shortcode attributes are parsed in `snapcre_filters()` function
2. Comma-separated values are split into arrays for multiselect fields
3. Parameters are passed to the filter template via `fcre_get_template_part()`
4. Template processes parameters and pre-populates form fields

### Filter Logic
1. Multiselect filters use `LIKE` comparison with JSON-encoded values
2. Range filters support both single values and ranges
3. Single range values use `>=` or `<=` operators
4. Full ranges use `BETWEEN` operator
5. Address filter uses `LIKE` comparison for partial matching

### Backward Compatibility
- All existing shortcode usage remains functional
- New parameters are optional
- Default behavior unchanged when no parameters provided

## Testing
Use the provided `shortcode-test.html` file to test various parameter combinations and verify functionality.
