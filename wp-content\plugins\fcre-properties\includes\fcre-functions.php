<?php
if (!function_exists('getIPAddress')) {
    function getIPAddress()
    {
        //whether ip is from the share internet
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } //whether ip is from the proxy
        elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } //whether ip is from the remote address
        else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        if ($ip == '::1') {
            $ip = '127.0.0.1';
        }
        return $ip;
    }
}

if(!function_exists('fcre_get_option_label_from_meta')) {
function fcre_get_option_label_from_meta($post_id, $meta_key, $option_key)
{
    $meta_value = get_post_meta($post_id, $meta_key, true);
    $options = get_option($option_key);

    if (empty($options) || !is_array($options)) {
        return '';
    }

    $selected_ids = is_array($meta_value) ? $meta_value : (array) $meta_value;

    $labels = [];

    foreach ($options as $option) {
        if (isset($option['id'], $option['name']) && in_array($option['id'], $selected_ids)) {
            $labels[] = $option['name'];
        }
    }

    return implode(', ', $labels);
}
}


if(!function_exists('is_from_current_website')) {
    function is_from_current_website()
    {
        $ref = (isset($_SERVER['HTTP_REFERER'])) ? $_SERVER['HTTP_REFERER'] : '';
        $refData = parse_url($ref);

        $refDomain = (isset($refData['host'])) ? $refData['host'] : '';

        $urlparts = parse_url(home_url());
        $domain = $urlparts['host'];

        if ($refDomain == $domain) {
            return true;
        } else {
            return false;
        }
    }
}


/**
 * The below function will help to load template file from plugin directory of wordpress
 *  Extracted from : http://wordpress.stackexchange.com/questions/94343/get-template-part-from-plugin
 */
function fcre_get_template_part($slug, $name = null, $args = null)
{

    do_action("fcre_get_template_part_{$slug}", $slug, $name, $args);

    $templates = array();
    if (isset($name))
        $templates[] = "{$slug}-{$name}.php";

    $templates[] = "{$slug}.php";

    fcre_get_template_path($templates, true, false, $args);
}

/* Extend locate_template from WP Core
* Define a location of your plugin file dir to a constant in this case = PLUGIN_DIR_PATH
* Note: PLUGIN_DIR_PATH - can be any folder/subdirectory within your plugin files
*/

function fcre_get_template_path($template_names, $load = false, $require_once = true, $args)
{
    $located = '';
    foreach ((array) $template_names as $template_name) {
        if (!$template_name)
            continue;

        /* search file within the PLUGIN_DIR_PATH only */
        if (file_exists(plugin_dir_path(__DIR__) . '/public/template-parts/' . $template_name)) {
            $located = plugin_dir_path(__DIR__) . '/public/template-parts/' . $template_name;
            break;
        }
    }

    if ($load && '' != $located) {
        load_template($located, $require_once, $args);
    }

    return $located;
}


function snapcre_folder_path()
{
    $upload = wp_upload_dir();
    $upload_dir = $upload['basedir'];
    $upload_dir = $upload_dir . '/fcre-properties';
    return $upload_dir;
}

 function snapcre_folder_url()
 {
     $upload = wp_upload_dir();
     $upload_dir = $upload['baseurl'];
     $upload_dir = $upload_dir . '/fcre-properties';
     return $upload_dir;
 }

/**
 * Convert attachment ID to URL
 * Helper function for backward compatibility with file upload fields
 *
 * @param int $attachment_id The attachment ID
 * @return string The attachment URL or empty string if not found
 */
if (!function_exists('fcre_get_attachment_url_from_id')) {
    function fcre_get_attachment_url_from_id($attachment_id) {
        if (empty($attachment_id)) {
            return '';
        }

        // If it's already a URL, return as is (for backward compatibility)
        if (filter_var($attachment_id, FILTER_VALIDATE_URL)) {
            return $attachment_id;
        }

        // Convert attachment ID to URL
        $url = wp_get_attachment_url($attachment_id);
        return $url ? $url : '';
    }
}

/**
 * Get file details from attachment ID
 * Returns an array with file information
 *
 * @param int $attachment_id The attachment ID
 * @return array|false Array with file details or false if not found
 */
if (!function_exists('fcre_get_file_details_from_id')) {
    function fcre_get_file_details_from_id($attachment_id) {
        if (empty($attachment_id)) {
            return false;
        }

        $attachment = get_post($attachment_id);
        if (!$attachment) {
            return false;
        }

        $file_path = get_attached_file($attachment_id);
        if (!$file_path || !file_exists($file_path)) {
            return false;
        }

        $file_url = wp_get_attachment_url($attachment_id);
        $file_name = basename($file_path);
        $file_size = size_format(filesize($file_path));
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        return [
            'id' => $attachment_id,
            'url' => $file_url,
            'name' => $file_name,
            'size' => $file_size,
            'ext' => $file_ext,
            'path' => $file_path,
            'title' => $attachment->post_title,
            'description' => $attachment->post_content,
            'caption' => $attachment->post_excerpt
        ];
    }
}

function fcre_send_email($to, $subject, $message, $attachment = '')
{
    $FCRE = Fcre_global::getInstance();
    $headers = array('Content-Type: text/html; charset=UTF-8');
    $headers[] = 'From: ' . $FCRE->email_from_name . ' <' . $FCRE->email_from_email . '>';
    $headers[] = 'Reply-To: ' . $FCRE->email_from_name . ' <' . $FCRE->email_from_email . '>';
    $headers[] = 'Bcc: ' . $FCRE->email_bcc;
    $headers[] = 'Cc: ' . $FCRE->email_cc;
    $headers[] = 'X-Mailer: PHP/' . phpversion();
    $headers[] = 'X-Priority: 1';
    $headers[] = 'X-MSMail-Priority: High';
    $headers[] = 'Importance: High';
    $headers[] = 'MIME-Version: 1.0';
    $headers[] = 'Content-Type: text/html; charset=UTF-8';
    $headers[] = 'Content-Transfer-Encoding: 8bit';
    $headers[] = 'Date: ' . date('r', $_SERVER['REQUEST_TIME']);
    $headers[] = 'X-MS-Has-Attach: ' . (!empty($attachment) ? 'yes' : 'no');
    $headers[] = 'X-MS-TNEF-Correlator: ';

    $mail = wp_mail($to, $subject, $message, $headers, $attachment);
    return $mail;
}


function fcre_frontend_multiselect($args = []) {
    $defaults = [
        'option_key'  => '',
        'field_name'  => '',
        'label'       => '',
        'placeholder' => 'Select',
        'group_class' => '',
        'selected_values' => [], // Array of pre-selected values
    ];
    $args = wp_parse_args($args, $defaults);

    if (empty($args['option_key']) || empty($args['field_name'])) {
        echo 'Missing required parameters.';
        return;
    }

    $options = get_option($args['option_key']);
    ?>
    <div class="fcre-multiselect-wrapper <?php echo esc_attr($args['group_class']); ?>">
        <?php if (!empty($args['label'])): ?>
            <label class="fcre-filter-label"><?php echo esc_html($args['label']); ?></label>
        <?php endif; ?>

        <div class="filter-select" data-placeholder="<?php echo esc_attr($args['placeholder']); ?>">
            <span class="filter-placeholder"></span>
            <span class="filter-select-arrow"></span>
            <span class="filter-onclick"></span>

            <div class="filter-dropdown fcre-hide">
                <div class="filter-dropdown-area">
                    <input type="checkbox" class="fcre-select-all" data-group-selector=".fcre-<?php echo esc_attr($args['field_name']); ?>-items" id="fcre-<?php echo esc_attr($args['field_name']); ?>-all">
                    <label for="fcre-<?php echo esc_attr($args['field_name']); ?>-all" class="text-black">All</label>
                    <ul class="fcre-dropdown">
                        <?php if (!empty($options) && is_array($options)): ?>
                            <?php foreach ($options as $index => $option): ?>
                                <?php
                                $is_checked = in_array($option['id'], $args['selected_values']) ? 'checked' : '';
                                ?>
                                <li>
                                    <input
                                        type="checkbox"
                                        class="fcre-<?php echo esc_attr($args['field_name']); ?>-items"
                                        data-name="<?php echo esc_attr($option['name']); ?>"
                                        name="<?php echo esc_attr($args['field_name']); ?>"
                                        id="fcre-<?php echo esc_attr($args['field_name']); ?>-<?php echo $index; ?>"
                                        value="<?php echo esc_attr($option['id']); ?>"
                                        <?php echo $is_checked; ?>
                                    >
                                    <label for="fcre-<?php echo esc_attr($args['field_name']); ?>-<?php echo $index; ?>" class="text-black">
                                        <?php echo esc_html($option['name']); ?>
                                    </label>
                                </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <?php
}

function fcre_get_agent_emails($post_id)
{
    $FCRE = Fcre_global::getInstance();

    $related_agents = get_post_meta($post_id, 'related_agents', true);
    $related_agents = array_filter(explode(',', $related_agents));

    $agentEmailsArray = array();
    $agentEmails = '';

    if($related_agents){
        foreach($related_agents as $agent_id){
            $email = get_post_meta($agent_id, 'email', true);
            if ($email) {
                array_push($agentEmailsArray, $email);
            }
        }
        $agentEmails = implode(',', $agentEmailsArray);
    }


    if ($FCRE->ask_question_custom_flag == 1 && $FCRE->ask_question_agent_flag == 1) {
        $agentEmails = $agentEmails . ',' . $FCRE->ask_question_emails;
    } elseif ($FCRE->ask_question_custom_flag == 1) {
        $agentEmails = $FCRE->ask_question_emails;
    } elseif ($FCRE->ask_question_agent_flag == 1) {
        $agentEmails = $agentEmails;
    }
    return $agentEmails;
}

/**
 * Get all available property detail fields configuration
 *
 * @return array Array of all available field configurations
 */
if (!function_exists('fcre_get_available_property_fields')) {
    function fcre_get_available_property_fields() {
        $FCRE = Fcre_Global::getInstance();

        // Define all available property detail fields - SINGLE SOURCE OF TRUTH
        return [
            'property_type' => [
                'label' => 'Property Type',
                'meta_key' => 'property_types',
                'type' => 'dynamic',
                'option_key' => $FCRE->plugin_name . '-property-types'
            ],
            'transaction_type' => [
                'label' => 'Transaction Type',
                'meta_key' => 'transaction_types',
                'type' => 'dynamic',
                'option_key' => $FCRE->plugin_name . '-transaction-types'
            ],
            'address' => [
                'label' => 'Address',
                'meta_key' => 'address',
                'type' => 'text'
            ],
            'site' => [
                'label' => 'Site Size',
                'meta_key' => 'site_size',
                'type' => 'text'
            ],
            'building' => [
                'label' => 'Building',
                'meta_key' => 'building',
                'type' => 'text'
            ],
            'building_size' => [
                'label' => 'Building Size',
                'meta_key' => 'building_size',
                'type' => 'number'
            ],
            'building_class' => [
                'label' => 'Building Class',
                'meta_key' => 'building_class',
                'type' => 'text'
            ],
            'lease_rate' => [
                'label' => 'Lease Rate',
                'meta_key' => 'lease_rate',
                'type' => 'text'
            ],
            'year_built' => [
                'label' => 'Year Built',
                'meta_key' => 'year_built',
                'type' => 'number'
            ],
            'lease_rate_suffix' => [
                'label' => 'Lease Rate Suffix',
                'meta_key' => 'lease_rate_suffix',
                'type' => 'text'
            ],
            'price' => [
                'label' => 'Price',
                'meta_key' => 'price',
                'type' => 'number'
            ],
            'occupancy' => [
                'label' => 'Occupancy',
                'meta_key' => 'occupancy',
                'type' => 'text'
            ],
            'cap_rate' => [
                'label' => 'Cap Rate',
                'meta_key' => 'cap_rate',
                'type' => 'number'
            ],
            'noi' => [
                'label' => 'NOI',
                'meta_key' => 'noi',
                'type' => 'number'
            ],
            'parking_ratio' => [
                'label' => 'Parking Ratio',
                'meta_key' => 'parking_ratio',
                'type' => 'text'
            ],
            'total_parking_spaces' => [
                'label' => 'Total Parking Spaces',
                'meta_key' => 'total_parking_spaces',
                'type' => 'text'
            ],
            'loading_docks' => [
                'label' => 'Loading Docks',
                'meta_key' => 'loading_docks',
                'type' => 'text'
            ],
            'zoning' => [
                'label' => 'Zoning',
                'meta_key' => 'zoning',
                'type' => 'text'
            ],
            'sprinkler_system' => [
                'label' => 'Sprinkler System',
                'meta_key' => 'sprinkler_system',
                'type' => 'text'
            ],
            'hvac_type' => [
                'label' => 'HVAC Type',
                'meta_key' => 'hvac_type',
                'type' => 'text'
            ],
            'power' => [
                'label' => 'Power',
                'meta_key' => 'power',
                'type' => 'text'
            ],
            'connectivity' => [
                'label' => 'Fiber / Connectivity',
                'meta_key' => 'connectivity',
                'type' => 'text'
            ],
            'elevators' => [
                'label' => 'Elevators',
                'meta_key' => 'elevators',
                'type' => 'text'
            ]
        ];
    }
}

/**
 * Get sorted and visible property fields configuration
 *
 * @return array Array of field configurations in sorted order
 */
if (!function_exists('fcre_get_sorted_property_fields')) {
    function fcre_get_sorted_property_fields() {
        $FCRE = Fcre_Global::getInstance();

        // Get all available fields from single source
        $available_fields = fcre_get_available_property_fields();

        // Get saved field order and visibility
        $saved_field_order = get_option($FCRE->plugin_name . '-property-field-order', '');
        $saved_field_visibility = get_option($FCRE->plugin_name . '-property-field-visibility', array_keys($available_fields));

        // If no saved order, use default order
        if (empty($saved_field_order)) {
            $field_order = array_keys($available_fields);
        } else {
            $field_order = explode(',', $saved_field_order);
            // Ensure all fields are included (in case new fields were added)
            $field_order = array_merge($field_order, array_diff(array_keys($available_fields), $field_order));
        }

        // Filter to only include visible fields and return with full configuration
        $sorted_fields = [];
        foreach ($field_order as $field_key) {
            if (isset($available_fields[$field_key]) && in_array($field_key, $saved_field_visibility)) {
                $sorted_fields[$field_key] = $available_fields[$field_key];
            }
        }

        return $sorted_fields;
    }
}

/**
 * Render property detail fields in sorted order
 *
 * @param int $post_id The property post ID
 */
if (!function_exists('fcre_render_sorted_property_fields')) {
    function fcre_render_sorted_property_fields($post_id) {
        $sorted_fields = fcre_get_sorted_property_fields();

        foreach ($sorted_fields as $field_key => $field_config) {
            $meta_value = '';

            // Get the field value based on type
            if ($field_config['type'] === 'dynamic') {
                // For dynamic fields (property_type, transaction_type)
                $meta_value = fcre_get_option_label_from_meta($post_id, $field_config['meta_key'], $field_config['option_key']);
            } else {
                // For regular meta fields
                $meta_value = get_post_meta($post_id, $field_config['meta_key'], true);
                if($field_config['meta_key'] === 'price' && is_numeric($meta_value) && floatval($meta_value) > 0 ){
                    $meta_value = '$' . number_format($meta_value);
                } else if($field_config['meta_key'] === 'site_size' && is_numeric($meta_value) && floatval($meta_value) > 0 ){
                    $meta_value = $meta_value. ' Acres';
                } else if($field_config['meta_key'] === 'building_size' && is_numeric($meta_value) && floatval($meta_value) > 0 ){
                    $meta_value = number_format($meta_value). ' SF';
                } else if($field_config['meta_key'] === 'cap_rate' && is_numeric($meta_value) && floatval($meta_value) > 0 ){
                    $meta_value = $meta_value . '%';
                } else if($field_config['meta_key'] === 'noi' && is_numeric($meta_value) && floatval($meta_value) > 0 ){
                    $meta_value = '$' . number_format($meta_value);
                } else if($field_config['meta_key'] === 'lease_rate' && is_numeric($meta_value) && floatval($meta_value) > 0  ){
                    $lease_rate_suffix = get_post_meta($post_id, 'lease_rate_suffix', true);
                    $meta_value = '$' . number_format($meta_value) . ($lease_rate_suffix ? '' . $lease_rate_suffix : '');
                    // Skip the lease_rate_suffix field since we're combining it with lease_rate
                    if(isset($sorted_fields['lease_rate_suffix'])) {
                        unset($sorted_fields['lease_rate_suffix']);
                    }
                }
            }

            // Only display if field has a value and it's not lease_rate_suffix
            if (!empty($meta_value) && $field_config['meta_key'] !== 'lease_rate_suffix') {
                ?>
                <div class="fcre-single-property-overview-item">
                    <span class="fcre-single-property-overview-item-label"><?php echo esc_html($field_config['label']); ?>:</span>
                    <span class="fcre-single-property-overview-item-value"><?php echo esc_html($meta_value); ?></span>
                </div>
                <?php
            }
        }
    }
}