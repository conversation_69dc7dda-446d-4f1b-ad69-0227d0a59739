<?php

$address = get_post_meta($post->ID, 'address', true);
$site_size = get_post_meta($post->ID, 'site_size', true);
$building = get_post_meta($post->ID, 'building', true);
$building_size = get_post_meta($post->ID, 'building_size', true);
$building_class = get_post_meta($post->ID, 'building_class', true);
$lease_rate = get_post_meta($post->ID, 'lease_rate', true);
$building_rsf = get_post_meta($post->ID, 'building_rsf', true);
$number_of_buildings = get_post_meta($post->ID, 'number_of_buildings', true);
$floors = get_post_meta($post->ID, 'floors', true);
$ceiling_height = get_post_meta($post->ID, 'ceiling_height', true);
$column_spacing = get_post_meta($post->ID, 'column_spacing', true);
$year_renovated = get_post_meta($post->ID, 'year_renovated', true);
$year_built = get_post_meta($post->ID, 'year_built', true);
$property_status = get_post_meta($post->ID, 'status', true);
$latitude = get_post_meta($post->ID, 'latitude', true);
$longitude = get_post_meta($post->ID, 'longitude', true);
$lease_rate_suffix = get_post_meta($post->ID, 'lease_rate_suffix', true);
$price = get_post_meta($post->ID, 'price', true);
$occupancy = get_post_meta($post->ID, 'occupancy', true);
$cap_rate = get_post_meta($post->ID, 'cap_rate', true);
$noi = get_post_meta($post->ID, 'noi', true);
$parking_ratio = get_post_meta($post->ID, 'parking_ratio', true);
$total_parking_spaces = get_post_meta($post->ID, 'total_parking_spaces', true);
$loading_docks = get_post_meta($post->ID, 'loading_docks', true);
$zoning = get_post_meta($post->ID, 'zoning', true);
$sprinkler_system = get_post_meta($post->ID, 'sprinkler_system', true);
$hvac_type = get_post_meta($post->ID, 'hvac_type', true);
$power = get_post_meta($post->ID, 'power', true);
$connectivity = get_post_meta($post->ID, 'connectivity', true);
$elevators = get_post_meta($post->ID, 'elevators', true);



?>
<div class="fcre-container">
    <fieldset>
        <legend>Site & Building Data</legend>
        <div class="fcre-row">

         <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="address"><?php _e('Address', 'textdomain'); ?></label>
                    <input type="text" id="address" name="address" value="<?php echo esc_attr($address); ?>" />
                </div>
            </div>
        <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="latitude"><?php _e('Latitude', 'textdomain'); ?></label>
                    <input type="text" id="latitude" name="latitude" value="<?php echo esc_attr($latitude); ?>" />
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="longitude"><?php _e('Longitude', 'textdomain'); ?></label>
                    <input type="text" id="longitude" name="longitude" value="<?php echo esc_attr($longitude); ?>" />
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <?php
                    fcre_dynamic_multiselect($post, [
                        'option_key'  => $this->FCRE->plugin_name . '-property-types',
                        'meta_key'    => 'property_types',
                        'field_name'  => 'property_types',
                        'label'       => 'Property Types',
                        'placeholder' => 'Select Property Types',
                    ]);
                    ?>
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <?php
                    fcre_dynamic_multiselect($post, [
                        'option_key'  => $this->FCRE->plugin_name . '-transaction-types',
                        'meta_key'    => 'transaction_types',
                        'field_name'  => 'transaction_types',
                        'label'       => 'Transaction Types',
                        'placeholder' => 'Select Transaction Types',
                    ]);
                    ?>
                </div>
            </div>

             <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <?php
                    fcre_dynamic_multiselect($post, [
                        'option_key'  => $this->FCRE->plugin_name . '-property-status',
                        'meta_key'    => 'property_status',
                        'field_name'  => 'property_status',
                        'label'       => 'Property Status',
                        'placeholder' => 'Select Property Status',
                    ]);
                    ?>
                </div>
            </div>
           
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="site_size"><?php _e('Site Size', 'textdomain'); ?></label>
                    <input type="number" id="site_size" name="site_size" value="<?php echo esc_attr($site_size); ?>" step="0.01" />
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="building_size"><?php _e('Total Building Size (GSF)', 'textdomain'); ?></label>
                    <input type="number" id="building_size" name="building_size" value="<?php echo esc_attr($building_size); ?>" step="0.01" />
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="building_rsf"><?php _e('Building RSF (Rentable SF)', 'textdomain'); ?></label>
                    <input type="number" id="building_rsf" name="building_rsf" value="<?php echo esc_attr($building_rsf); ?>" step="0.01" />
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="number_of_buildings"><?php _e('Number Of Buildings)', 'textdomain'); ?></label>
                    <input type="number" id="number_of_buildings" name="number_of_buildings" value="<?php echo esc_attr($number_of_buildings); ?>" step="0.01" />
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="floors"><?php _e('Floors', 'textdomain'); ?></label>
                    <input type="number" id="floors" name="floors" value="<?php echo esc_attr($floors); ?>" step="0.01"/>
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="ceiling_height"><?php _e('Ceiling Height / Clear Height', 'textdomain'); ?></label>
                    <input type="number" id="ceiling_height" name="ceiling_height" value="<?php echo esc_attr($ceiling_height); ?>" step="0.01"/>
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="column_spacing"><?php _e('Column Spacing', 'textdomain'); ?></label>
                    <input type="number" id="column_spacing" name="column_spacing" value="<?php echo esc_attr($column_spacing); ?>" step="0.01"/>
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="year_built"><?php _e('Year Built', 'textdomain'); ?></label>
                    <input type="number" id="year_built" name="year_built" value="<?php echo esc_attr($year_built); ?>" step="0.01"/>
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="year_renovated"><?php _e('Year Renovated', 'textdomain'); ?></label>
                    <input type="number" id="year_renovated" name="year_renovated" value="<?php echo esc_attr($year_renovated); ?>" step="0.01"/>
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="building_class"><?php _e('Building Class', 'textdomain'); ?></label>
                    <input type="text" id="building_class" name="building_class" value="<?php echo esc_attr($building_class); ?>" />
                </div>
            </div>

        </div>
    </fieldset>
     <fieldset>
        <legend>Financials</legend>
            <div class="fcre-row">
                <div class="fcre-col fcre-col-4">
                    <div class="fcre-form-group">
                        <label for="lease_rate"><?php _e('Lease Rate', 'textdomain'); ?></label>
                        <div class="fcre-input-group">
                            <input type="number" id="lease_rate" name="lease_rate" value="<?php echo esc_attr($lease_rate); ?>" step="0.01"/>
                            <input type="text" id="lease_rate_suffix" name="lease_rate_suffix" value="<?php echo esc_attr($lease_rate_suffix); ?>" placeholder="SF/ NNN / Negotiable" />
                        </div>
                        
                    </div>
                </div>
                <div class="fcre-col fcre-col-4">
                    <div class="fcre-form-group">
                        <label for="price"><?php _e('Sale Price / Asking Price', 'textdomain'); ?></label>
                        <input type="number" id="price" name="price" value="<?php echo esc_attr($price); ?>" step="0.01"/>
                    </div>
                </div>
                <div class="fcre-col fcre-col-4">
                    <div class="fcre-form-group">
                        <label for="occupancy"><?php _e('Occupancy', 'textdomain'); ?></label>
                        <input type="text" id="occupancy" name="occupancy" value="<?php echo esc_attr($occupancy); ?>" />
                    </div>
                </div>
                <div class="fcre-col fcre-col-4">
                    <div class="fcre-form-group">
                        <label for="cap_rate"><?php _e('Cap Rate', 'textdomain'); ?></label>
                        <input type="number" id="cap_rate" name="cap_rate" value="<?php echo esc_attr($cap_rate); ?>" step="0.01"/>
                    </div>
                </div>
                <div class="fcre-col fcre-col-4">
                    <div class="fcre-form-group">
                        <label for="noi"><?php _e('NOI', 'textdomain'); ?></label>
                        <input type="number" id="noi" name="noi" value="<?php echo esc_attr($noi); ?>" step="0.01"/>
                    </div>
                </div>
                 <div class="fcre-col fcre-col-4">
                    <div class="fcre-form-group">
                        <?php
                        fcre_dynamic_multiselect($post, [
                            'option_key'  => $this->FCRE->plugin_name . '-lease-types',
                            'meta_key'    => 'lease_types',
                            'field_name'  => 'lease_types',
                            'label'       => 'Lease Types',
                            'placeholder' => 'Select Lease Types',
                        ]);
                        ?>
                    </div>
                </div>

            </div>
     </fieldset>
     <fieldset>
        <legend>Access & Parking</legend>
        <div class="fcre-row">
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="parking_ratio"><?php _e('Parking Ratio', 'textdomain'); ?></label>
                    <input type="text" id="parking_ratio" name="parking_ratio" value="<?php echo esc_attr($parking_ratio); ?>" />
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="total_parking_spaces"><?php _e('Total Parking Spaces', 'textdomain'); ?></label>
                    <input type="text" id="parking_ratio" name="total_parking_spaces" value="<?php echo esc_attr($total_parking_spaces); ?>" />
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="loading_docks"><?php _e('Loading Docks / Drive-In Doors', 'textdomain'); ?></label>
                    <input type="text" id="loading_docks" name="loading_docks" value="<?php echo esc_attr($loading_docks); ?>" />
                </div>
            </div>
        </div>
     </fieldset>
     <fieldset>
        <legend>Utilities & Features</legend>
        <div class="fcre-row">
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="zoning"><?php _e('Zoning', 'textdomain'); ?></label>
                    <input type="text" id="zoning" name="zoning" value="<?php echo esc_attr($zoning); ?>" />
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="sprinkler_system"><?php _e('Sprinkler System', 'textdomain'); ?></label>
                    <input type="text" id="sprinkler_system" name="sprinkler_system" value="<?php echo esc_attr($sprinkler_system); ?>" />
                </div>
            </div>

            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="hvac_type"><?php _e('HVAC Type', 'textdomain'); ?></label>
                    <input type="text" id="hvac_type" name="hvac_type" value="<?php echo esc_attr($hvac_type); ?>" />
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="power"><?php _e('Power', 'textdomain'); ?></label>
                    <input type="text" id="power" name="power" value="<?php echo esc_attr($power); ?>" />
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="connectivity"><?php _e('Fiber / Connectivity', 'textdomain'); ?></label>
                    <input type="text" id="connectivity" name="connectivity" value="<?php echo esc_attr($connectivity); ?>" />
                </div>
            </div>
            <div class="fcre-col fcre-col-4">
                <div class="fcre-form-group">
                    <label for="elevators"><?php _e('Elevators', 'textdomain'); ?></label>
                    <input type="text" id="elevators" name="elevators" value="<?php echo esc_attr($elevators); ?>" />
                </div>
            </div>
        </div>
     </fieldset>
    <fieldset>
        <legend>Others</legend>
        <div class="fcre-row">
            
            <div class="fcre-col fcre-col-12">
                <div class="fcre-form-group">
                    <?php
                    fcre_file_upload_field($post, [
                        'meta_key'    => 'property_flyer',
                        'field_name'  => 'property_flyer',
                        'label'       => 'Upload Flyer',
                        'placeholder' => 'Upload Flyer',
                        'group_class' => 'mb-3',
                    ]);
                    ?>
                </div>
            </div>
            <div class="fcre-col fcre-col-12">
                <div class="fcre-form-group">
                    <?php
                    fcre_file_upload_field($post, [
                        'meta_key'    => 'property_om',
                        'field_name'  => 'property_om',
                        'label'       => 'Upload Offering Memorandum',
                        'placeholder' => 'Upload Offering Memorandum',
                        'group_class' => 'mb-3',
                    ]);
                    ?>
                </div>
            </div>
        </div>
        <div class="fcre-row">
            <div class="fcre-col fcre-col-6">
                <div class="fcre-form-group">
                    <?php
                    fcre_wysiwyg_field($post, [
                        'meta_key'    => 'virtual_tour',
                        'field_name'  => 'virtual_tour',
                        'label'       => 'Virtual Tour',
                        'group_class' => 'mb-4',
                        'editor_args' => [
                            'media_buttons' => false, // enable media uploader
                            'teeny'         => true // show full editor
                        ]
                    ]);
                    ?>
                </div>
            </div>
            <div class="fcre-col fcre-col-6">
                <div class="fcre-form-group">
                    <?php
                    fcre_wysiwyg_field($post, [
                        'meta_key'    => 'property_video',
                        'field_name'  => 'property_video',
                        'label'       => 'Property Video',
                        'group_class' => 'mb-4',
                        'editor_args' => [
                            'media_buttons' => false, // enable media uploader
                            'teeny'         => true // show full editor
                        ]
                    ]);
                    ?>
                </div>
            </div>
        </div>
    </fieldset>
</div>