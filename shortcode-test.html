<!DOCTYPE html>
<html>
<head>
    <title>SnapCRE Properties Shortcode Test</title>
</head>
<body>
    <h1>Enhanced SnapCRE Properties Shortcode Test</h1>
    
    <h2>Test Cases:</h2>
    
    <h3>1. Basic shortcode (no parameters)</h3>
    <code>[snapcre_properties]</code>
    
    <h3>2. Property types only</h3>
    <code>[snapcre_properties property_types="1,2,3"]</code>
    
    <h3>3. Transaction types only</h3>
    <code>[snapcre_properties transaction_types="1,2"]</code>
    
    <h3>4. Property and transaction types</h3>
    <code>[snapcre_properties property_types="1,2" transaction_types="1"]</code>
    
    <h3>5. Property status filter</h3>
    <code>[snapcre_properties property_status="1,2"]</code>
    
    <h3>6. Address search</h3>
    <code>[snapcre_properties address="New York"]</code>
    
    <h3>7. Building size range</h3>
    <code>[snapcre_properties min_sf="1000" max_sf="5000"]</code>
    
    <h3>8. Year built range</h3>
    <code>[snapcre_properties min_year="2000" max_year="2020"]</code>
    
    <h3>9. Min building size only</h3>
    <code>[snapcre_properties min_sf="2000"]</code>
    
    <h3>10. Max year built only</h3>
    <code>[snapcre_properties max_year="2010"]</code>
    
    <h3>11. All parameters combined</h3>
    <code>[snapcre_properties property_types="1,2" transaction_types="1" property_status="2" address="Downtown" min_sf="1500" max_sf="4000" min_year="2005" max_year="2018"]</code>
    
    <h2>Expected Behavior:</h2>
    <ul>
        <li>All shortcodes should render the properties filter form</li>
        <li>Form fields should be pre-populated with the specified values</li>
        <li>Multiselect fields should show selected options</li>
        <li>Text and number inputs should display the provided values</li>
        <li>Filtering should work correctly with the pre-selected values</li>
        <li>Results should be filtered according to the shortcode parameters</li>
    </ul>
</body>
</html>
