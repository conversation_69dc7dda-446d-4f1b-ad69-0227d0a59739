<?php

/**
 * The public-facing functionality of the plugin.
 *
 * @link       https://focusedcre.com
 * @since      1.0.0
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/public
 */

/**
 * The public-facing functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the public-facing stylesheet and JavaScript.
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/public
 * <AUTHOR> CRE <<EMAIL>>
 */
class Fcre_Properties_Public
{

	private $FCRE;
	private $plugin_name;
	private $version;

	public function __construct()
	{

		$this->FCRE = Fcre_Global::getInstance();
		$this->plugin_name = $this->FCRE->plugin_name;
		$this->version = $this->FCRE->version;
	}

	/**
	 * Register the stylesheets for the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_styles()
	{

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Fcre_Properties_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Fcre_Properties_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_style('leaflet-css', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css', array(), null, 'all');
		wp_enqueue_style('fotorama-css', 'https://cdnjs.cloudflare.com/ajax/libs/fotorama/4.6.4/fotorama.css', array(), null, 'all');
		wp_enqueue_style($this->plugin_name, plugin_dir_url(__FILE__) . 'css/fcre-properties-public.css', array(), $this->version, 'all');

	}

	/**
	 * Add custom CSS styles based on color settings
	 *
	 * @since    1.0.0
	 */
	public function add_custom_colors_css()
	{
		// Get saved color values
		$primary_color = get_option($this->plugin_name . '-primary-color', '#82e2df');
		$secondary_color = get_option($this->plugin_name . '-secondary-color', '#e31083');
		$button_color = get_option($this->plugin_name . '-button-color', '#82e2df');
		$button_hover_color = get_option($this->plugin_name . '-button-hover-color', '#e31083');
		$button_text_color = get_option($this->plugin_name . '-button-text-color', '#ffffff');
		$button_hover_text_color = get_option($this->plugin_name . '-button-hover-text-color', '#ffffff');

		// Generate custom CSS
		$custom_css = "
		<style type='text/css' id='fcre-custom-colors'>
		:root {
			--fcre-primary-color: {$primary_color};
			--fcre-secondary-color: {$secondary_color};
			--fcre-button-color: {$button_color};
			--fcre-button-hover-color: {$button_hover_color};
			--fcre-button-text-color: {$button_text_color};
			--fcre-button-hover-text-color: {$button_hover_text_color};
		}
		.fcre-form-control:focus,
		.filter-field input:focus,
		.filter-field select:focus {
			box-shadow: 0 0 0 0.25rem rgba(" . $this->hex_to_rgb($primary_color) . ", 0.25) !important;
		}
		.marker-cluster-small {
			background-color: rgba(" . $this->hex_to_rgb($secondary_color) . ",0.6);
		}

		.marker-cluster-small div {
			background-color: rgba(" . $this->hex_to_rgb($secondary_color) . ",0.6);
		}

		.marker-cluster-medium {
			background-color: rgba(" . $this->hex_to_rgb($secondary_color) . ",0.6);
		}

		.marker-cluster-medium div {
			background-color: rgba(" . $this->hex_to_rgb($secondary_color) . ",0.6);
		}

		.marker-cluster-large {
			background-color: rgba(" . $this->hex_to_rgb($secondary_color) . ",0.6);
		}

		.marker-cluster-large div {
			background-color: rgba(" . $this->hex_to_rgb($secondary_color) . ",0.6);
		}
		.marker-cluster span {
			color: #fff;
		}
		</style>";

		echo $custom_css;
	}

	/**
	 * Convert hex color to RGB values
	 *
	 * @param string $hex Hex color code
	 * @return string RGB values separated by commas
	 */
	private function hex_to_rgb($hex)
	{
		$hex = ltrim($hex, '#');

		if (strlen($hex) == 3) {
			$hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
		}

		$r = hexdec(substr($hex, 0, 2));
		$g = hexdec(substr($hex, 2, 2));
		$b = hexdec(substr($hex, 4, 2));

		return "$r, $g, $b";
	}

	/**
	 * Register the JavaScript for the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_scripts()
	{

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Fcre_Properties_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Fcre_Properties_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_script('leaflet-js', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js', array('jquery'), null, true);
		wp_enqueue_script('fotorama-js', 'https://cdnjs.cloudflare.com/ajax/libs/fotorama/4.6.4/fotorama.js', array('jquery'), null, true);
		wp_enqueue_script('leaflet-markercluster-js', 'https://unpkg.com/leaflet.markercluster@1.5.3/dist/leaflet.markercluster.js', array('jquery'), null, true);
		wp_enqueue_script('leaflet-markercluster-default-js', 'https://unpkg.com/leaflet.markercluster@1.5.3/dist/MarkerCluster.Default.js', array('jquery'), null, true);
		wp_enqueue_script($this->plugin_name.'-public', plugin_dir_url(__FILE__) . 'js/fcre-properties-public.js', array('jquery'), $this->version, false);
		
		$FILTER_VAR = array(
			'admin_ajax' => admin_url('admin-ajax.php'),
			'marker_icon_url' => plugin_dir_url(__FILE__) .'/img/map-marker.png', 
		);

		wp_localize_script($this->plugin_name.'-public', 'FILTER_VAR', $FILTER_VAR);
	}

	public function snapcre_filters($atts = [])
	{

		$property_types = isset($atts['property_types']) ? $atts['property_types'] : '';
		$transaction_types = isset($atts['transaction_types']) ? $atts['transaction_types'] : '';

		ob_start();

		fcre_get_template_part('filters/fcre', 'filters', array('property_types' => $property_types, 'transaction_types' => $transaction_types));

		fcre_get_template_part('filters/fcre', 'results');

		return ob_get_clean();
	}


	
    /**
     *  Set the template for single page
     *
     * @since    1.0.0
     */
    function fcre_single_post_template($single)
    {
        global $post;
        if ($post->post_type == $this->FCRE->properties_custom_post_slug) {
            $single = dirname(__FILE__) . '/templates/single-property.php';
        }
        if ($post->post_type == $this->FCRE->agent_custom_post_slug) {
            $single = dirname(__FILE__) . '/templates/single-agent.php';
        }
        return $single;
    }
}
