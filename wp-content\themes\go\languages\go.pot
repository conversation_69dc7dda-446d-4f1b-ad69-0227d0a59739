# Copyright (c) GoDaddy Operating Company, LLC. All Rights Reserved.
msgid ""
msgstr ""
"Project-Id-Version: Go 1.8.16\n"
"Report-Msgid-Bugs-To: https://github.com/godaddy-wordpress/go/issues\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-07-22T19:41:45+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.12.0\n"
"X-Domain: go\n"

#. Theme Name of the theme
#: style.css
msgid "Go"
msgstr ""

#. Theme URI of the theme
#: style.css
msgid "https://github.com/godaddy-wordpress/go"
msgstr ""

#. Description of the theme
#: style.css
msgid "Go is an innovative, Gutenberg-first WordPress theme, hyper-focused on empowering makers to build beautifully rich websites with WordPress."
msgstr ""

#. Author of the theme
#: style.css
msgid "GoDaddy"
msgstr ""

#. Author URI of the theme
#: style.css
msgid "https://www.godaddy.com"
msgstr ""

#. translators: %s: The post title.
#: comments.php:32
#, php-format
msgid "One thought on &ldquo;%s&rdquo;"
msgstr ""

#. translators: %1$s: The number of comments. %2$s: The post title.
#: comments.php:39
#, php-format
msgid "%1$s thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] ""
msgstr[1] ""

#: comments.php:61
msgid "Comment navigation"
msgstr ""

#: comments.php:62
msgid "&larr; Older Comments"
msgstr ""

#: comments.php:63
msgid "Newer Comments &rarr;"
msgstr ""

#: comments.php:74
msgid "Comments are closed."
msgstr ""

#: docs/design-styles/example/functions.php:26
msgctxt "design style name"
msgid "Brutalist"
msgstr ""

#: docs/design-styles/example/functions.php:31
msgctxt "color palette name"
msgid "Millennial"
msgstr ""

#: docs/design-styles/example/functions.php:38
#: includes/core.php:771
msgctxt "color palette name"
msgid "Blush"
msgstr ""

#: header.php:41
msgid "Skip to content"
msgstr ""

#: header.php:59
msgid "Horizontal"
msgstr ""

#: includes/amp.php:99
#: includes/amp.php:103
msgid "collapse child menu"
msgstr ""

#: includes/amp.php:100
#: includes/amp.php:103
msgid "expand child menu"
msgstr ""

#: includes/classes/admin/class-go-theme-deactivation.php:104
msgid "Please do not include any personal information in your submission. We do not collect or need this information. Please see our <a href='https://www.godaddy.com/legal/agreements/privacy-policy' target='_blank'>privacy policy</a> for details."
msgstr ""

#: includes/classes/admin/class-go-theme-deactivation.php:105
msgid "Skip & Switch Theme"
msgstr ""

#: includes/classes/admin/class-go-theme-deactivation.php:106
msgid "Submit & Switch Theme"
msgstr ""

#: includes/classes/admin/class-go-theme-deactivation.php:107
msgid "Thanks for trying Go. Let us know how we can improve."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:325
msgid "Install Required Plugins"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:326
msgid "Install Plugins"
msgstr ""

#. translators: %s: plugin name.
#: includes/classes/class-tgm-plugin-activation.php:328
#, php-format
msgid "Installing Plugin: %s"
msgstr ""

#. translators: %s: plugin name.
#: includes/classes/class-tgm-plugin-activation.php:330
#, php-format
msgid "Updating Plugin: %s"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:331
msgid "Something went wrong with the plugin API."
msgstr ""

#. translators: 1: plugin name(s).
#: includes/classes/class-tgm-plugin-activation.php:332
#, php-format
msgid "This theme requires the following plugin: %1$s."
msgid_plural "This theme requires the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name(s).
#: includes/classes/class-tgm-plugin-activation.php:338
#, php-format
msgid "This theme recommends the following plugin: %1$s."
msgid_plural "This theme recommends the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name(s).
#: includes/classes/class-tgm-plugin-activation.php:344
#, php-format
msgid "The following plugin needs to be updated to its latest version to ensure maximum compatibility with this theme: %1$s."
msgid_plural "The following plugins need to be updated to their latest version to ensure maximum compatibility with this theme: %1$s."
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name(s).
#: includes/classes/class-tgm-plugin-activation.php:350
#, php-format
msgid "There is an update available for: %1$s."
msgid_plural "There are updates available for the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name(s).
#: includes/classes/class-tgm-plugin-activation.php:356
#, php-format
msgid "The following required plugin is currently inactive: %1$s."
msgid_plural "The following required plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name(s).
#: includes/classes/class-tgm-plugin-activation.php:362
#, php-format
msgid "The following recommended plugin is currently inactive: %1$s."
msgid_plural "The following recommended plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#: includes/classes/class-tgm-plugin-activation.php:368
msgid "Begin installing plugin"
msgid_plural "Begin installing plugins"
msgstr[0] ""
msgstr[1] ""

#: includes/classes/class-tgm-plugin-activation.php:373
msgid "Begin updating plugin"
msgid_plural "Begin updating plugins"
msgstr[0] ""
msgstr[1] ""

#: includes/classes/class-tgm-plugin-activation.php:378
msgid "Begin activating plugin"
msgid_plural "Begin activating plugins"
msgstr[0] ""
msgstr[1] ""

#: includes/classes/class-tgm-plugin-activation.php:383
msgid "Return to Required Plugins Installer"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:384
#: includes/classes/class-tgm-plugin-activation.php:825
#: includes/classes/class-tgm-plugin-activation.php:2531
#: includes/classes/class-tgm-plugin-activation.php:3578
msgid "Return to the Dashboard"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:385
#: includes/classes/class-tgm-plugin-activation.php:3157
msgid "Plugin activated successfully."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:386
#: includes/classes/class-tgm-plugin-activation.php:2950
msgid "The following plugin was activated successfully:"
msgid_plural "The following plugins were activated successfully:"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name.
#: includes/classes/class-tgm-plugin-activation.php:388
#, php-format
msgid "No action taken. Plugin %1$s was already active."
msgstr ""

#. translators: 1: plugin name.
#: includes/classes/class-tgm-plugin-activation.php:390
#, php-format
msgid "Plugin not activated. A higher version of %s is needed for this theme. Please update the plugin."
msgstr ""

#. translators: 1: dashboard link.
#: includes/classes/class-tgm-plugin-activation.php:392
#, php-format
msgid "All plugins installed and activated successfully. %1$s"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:393
msgid "Dismiss this notice"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:394
msgid "There are one or more required or recommended plugins to install, update or activate."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:395
msgid "Please contact the administrator of this site for help."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:520
msgid "This plugin needs to be updated to be compatible with your theme."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:521
msgid "Update Required"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:932
msgid "The remote plugin package does not contain a folder with the desired slug and renaming did not work."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:932
#: includes/classes/class-tgm-plugin-activation.php:935
msgid "Please contact the plugin provider and ask them to package their plugin according to the WordPress guidelines."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:935
msgid "The remote plugin package consists of more than one file, but the files are not packaged in a folder."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:1119
#: includes/classes/class-tgm-plugin-activation.php:2946
msgctxt "plugin A *and* plugin B"
msgid "and"
msgstr ""

#. translators: %s: version number
#: includes/classes/class-tgm-plugin-activation.php:1980
#, php-format
msgid "TGMPA v%s"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2271
msgid "Required"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2274
msgid "Recommended"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2290
msgid "WordPress Repository"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2293
msgid "External Source"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2296
msgid "Pre-Packaged"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2313
msgid "Not Installed"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2317
msgid "Installed But Not Activated"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2319
msgid "Active"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2325
msgid "Required Update not Available"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2328
msgid "Requires Update"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2331
msgid "Update recommended"
msgstr ""

#. translators: 1: install status, 2: update status
#: includes/classes/class-tgm-plugin-activation.php:2340
#, php-format
msgctxt "Install/Update Status"
msgid "%1$s, %2$s"
msgstr ""

#. translators: 1: number of plugins.
#: includes/classes/class-tgm-plugin-activation.php:2386
#, php-format
msgctxt "plugins"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: number of plugins.
#: includes/classes/class-tgm-plugin-activation.php:2390
#, php-format
msgid "To Install <span class=\"count\">(%s)</span>"
msgid_plural "To Install <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: number of plugins.
#: includes/classes/class-tgm-plugin-activation.php:2394
#, php-format
msgid "Update Available <span class=\"count\">(%s)</span>"
msgid_plural "Update Available <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: number of plugins.
#: includes/classes/class-tgm-plugin-activation.php:2398
#, php-format
msgid "To Activate <span class=\"count\">(%s)</span>"
msgid_plural "To Activate <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: includes/classes/class-tgm-plugin-activation.php:2480
msgctxt "as in: \"version nr unknown\""
msgid "unknown"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2488
msgid "Installed version:"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2496
msgid "Minimum required version:"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2508
msgid "Available version:"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2531
msgid "No plugins to install, update or activate."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2545
msgid "Plugin"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2546
msgid "Source"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2547
msgid "Type"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2551
msgid "Version"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2552
msgid "Status"
msgstr ""

#. translators: %2$s: plugin name in screen reader markup
#: includes/classes/class-tgm-plugin-activation.php:2601
#, php-format
msgid "Install %2$s"
msgstr ""

#. translators: %2$s: plugin name in screen reader markup
#: includes/classes/class-tgm-plugin-activation.php:2606
#, php-format
msgid "Update %2$s"
msgstr ""

#. translators: %2$s: plugin name in screen reader markup
#: includes/classes/class-tgm-plugin-activation.php:2612
#, php-format
msgid "Activate %2$s"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2682
msgid "Upgrade message from the plugin author:"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2715
msgid "Install"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2721
msgid "Update"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2724
msgid "Activate"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2755
msgid "No plugins were selected to be installed. No action taken."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2757
msgid "No plugins were selected to be updated. No action taken."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2798
msgid "No plugins are available to be installed at this time."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2800
msgid "No plugins are available to be updated at this time."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2906
msgid "No plugins were selected to be activated. No action taken."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:2932
msgid "No plugins are available to be activated at this time."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:3156
msgid "Plugin activation failed."
msgstr ""

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: includes/classes/class-tgm-plugin-activation.php:3496
#, php-format
msgid "Updating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#. translators: 1: plugin name, 2: error message.
#: includes/classes/class-tgm-plugin-activation.php:3499
#, php-format
msgid "An error occurred while installing %1$s: <strong>%2$s</strong>."
msgstr ""

#. translators: 1: plugin name.
#: includes/classes/class-tgm-plugin-activation.php:3501
#, php-format
msgid "The installation of %1$s failed."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:3505
msgid "The installation and activation process is starting. This process may take a while on some hosts, so please be patient."
msgstr ""

#. translators: 1: plugin name.
#: includes/classes/class-tgm-plugin-activation.php:3507
#, php-format
msgid "%1$s installed and activated successfully."
msgstr ""

#. translators: 1: plugin name.
#: includes/classes/class-tgm-plugin-activation.php:3507
#: includes/classes/class-tgm-plugin-activation.php:3515
msgid "Show Details"
msgstr ""

#. translators: 1: plugin name.
#: includes/classes/class-tgm-plugin-activation.php:3507
#: includes/classes/class-tgm-plugin-activation.php:3515
msgid "Hide Details"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:3508
msgid "All installations and activations have been completed."
msgstr ""

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: includes/classes/class-tgm-plugin-activation.php:3510
#, php-format
msgid "Installing and Activating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:3513
msgid "The installation process is starting. This process may take a while on some hosts, so please be patient."
msgstr ""

#. translators: 1: plugin name.
#: includes/classes/class-tgm-plugin-activation.php:3515
#, php-format
msgid "%1$s installed successfully."
msgstr ""

#: includes/classes/class-tgm-plugin-activation.php:3516
msgid "All installations have been completed."
msgstr ""

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: includes/classes/class-tgm-plugin-activation.php:3518
#, php-format
msgid "Installing Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: includes/core.php:99
#: includes/customizer.php:548
msgid "Primary"
msgstr ""

#: includes/core.php:100
msgid "Footer Menu #1"
msgstr ""

#: includes/core.php:101
msgid "Footer Menu #2"
msgstr ""

#: includes/core.php:102
msgid "Footer Menu #3"
msgstr ""

#: includes/core.php:178
msgctxt "font size option label"
msgid "Small"
msgstr ""

#: includes/core.php:179
msgctxt "abbreviation of the font size option label"
msgid "S"
msgstr ""

#: includes/core.php:184
msgctxt "font size option label"
msgid "Medium"
msgstr ""

#: includes/core.php:185
msgctxt "abbreviation of the font size option label"
msgid "M"
msgstr ""

#: includes/core.php:190
msgctxt "font size option label"
msgid "Large"
msgstr ""

#: includes/core.php:191
msgctxt "abbreviation of the font size option label"
msgid "L"
msgstr ""

#: includes/core.php:196
msgctxt "font size option label"
msgid "Huge"
msgstr ""

#: includes/core.php:197
msgctxt "abbreviation of the font size option label"
msgid "XL"
msgstr ""

#: includes/core.php:209
msgctxt "name of the first color palette selection"
msgid "Primary"
msgstr ""

#: includes/core.php:214
msgctxt "name of the second color palette selection"
msgid "Secondary"
msgstr ""

#: includes/core.php:219
msgctxt "name of the third color palette selection"
msgid "Tertiary"
msgstr ""

#: includes/core.php:224
msgctxt "name of the fourth color palette selection"
msgid "Quaternary"
msgstr ""

#: includes/core.php:241
msgid "Primary to Secondary"
msgstr ""

#: includes/core.php:246
msgid "Primary to Tertiary"
msgstr ""

#: includes/core.php:251
msgid "Primary to Background"
msgstr ""

#: includes/core.php:256
msgid "Secondary to Tertiary"
msgstr ""

#: includes/core.php:706
msgctxt "design style name"
msgid "Traditional"
msgstr ""

#: includes/core.php:711
msgctxt "color palette name"
msgid "Apricot"
msgstr ""

#: includes/core.php:718
msgctxt "color palette name"
msgid "Emerald"
msgstr ""

#: includes/core.php:725
msgctxt "color palette name"
msgid "Brick"
msgstr ""

#: includes/core.php:732
msgctxt "color palette name"
msgid "Bronze"
msgstr ""

#: includes/core.php:759
msgctxt "design style name"
msgid "Modern"
msgstr ""

#: includes/core.php:764
msgctxt "color palette name"
msgid "Shade"
msgstr ""

#: includes/core.php:778
msgctxt "color palette name"
msgid "Indigo"
msgstr ""

#: includes/core.php:785
msgctxt "color palette name"
msgid "Pacific"
msgstr ""

#: includes/core.php:813
msgctxt "design style name"
msgid "Trendy"
msgstr ""

#: includes/core.php:818
msgctxt "color palette name"
msgid "Plum"
msgstr ""

#: includes/core.php:827
msgctxt "color palette name"
msgid "Steel"
msgstr ""

#: includes/core.php:835
msgctxt "color palette name"
msgid "Avocado"
msgstr ""

#: includes/core.php:843
msgctxt "color palette name"
msgid "Champagne"
msgstr ""

#: includes/core.php:872
msgctxt "design style name"
msgid "Welcoming"
msgstr ""

#: includes/core.php:877
msgctxt "color palette name"
msgid "Forest"
msgstr ""

#: includes/core.php:885
msgctxt "color palette name"
msgid "Spruce"
msgstr ""

#: includes/core.php:893
msgctxt "color palette name"
msgid "Mocha"
msgstr ""

#: includes/core.php:901
msgctxt "color palette name"
msgid "Lavender"
msgstr ""

#: includes/core.php:926
msgctxt "design style name"
msgid "Playful"
msgstr ""

#: includes/core.php:931
msgctxt "color palette name"
msgid "Frolic"
msgstr ""

#: includes/core.php:941
msgctxt "color palette name"
msgid "Coral"
msgstr ""

#: includes/core.php:951
msgctxt "color palette name"
msgid "Organic"
msgstr ""

#: includes/core.php:961
msgctxt "color palette name"
msgid "Berry"
msgstr ""

#: includes/core.php:1043
msgctxt "name of the first header variation option"
msgid "Header 1"
msgstr ""

#: includes/core.php:1047
msgctxt "name of the second header variation option"
msgid "Header 2"
msgstr ""

#: includes/core.php:1051
msgctxt "name of the third header variation option"
msgid "Header 3"
msgstr ""

#: includes/core.php:1055
msgctxt "name of the fourth header variation option"
msgid "Header 4"
msgstr ""

#: includes/core.php:1059
msgctxt "name of the fourth header variation option"
msgid "Header 5"
msgstr ""

#: includes/core.php:1063
msgctxt "name of the fourth header variation option"
msgid "Header 6"
msgstr ""

#: includes/core.php:1067
msgctxt "name of the fourth header variation option"
msgid "Header 7"
msgstr ""

#: includes/core.php:1093
msgctxt "name of the first footer variation option"
msgid "Footer 1"
msgstr ""

#: includes/core.php:1100
msgctxt "name of the second footer variation option"
msgid "Footer 2"
msgstr ""

#: includes/core.php:1107
msgctxt "name of the third footer variation option"
msgid "Footer 3"
msgstr ""

#: includes/core.php:1114
msgctxt "name of the fourth footer variation option"
msgid "Footer 4"
msgstr ""

#: includes/core.php:1193
msgid "Facebook"
msgstr ""

#: includes/core.php:1198
msgid "X"
msgstr ""

#: includes/core.php:1203
msgid "Instagram"
msgstr ""

#: includes/core.php:1208
msgid "LinkedIn"
msgstr ""

#: includes/core.php:1213
msgid "Xing"
msgstr ""

#: includes/core.php:1218
msgid "Pinterest"
msgstr ""

#: includes/core.php:1223
msgid "YouTube"
msgstr ""

#: includes/core.php:1228
msgid "Spotify"
msgstr ""

#: includes/core.php:1233
msgid "GitHub"
msgstr ""

#: includes/core.php:1238
msgid "TikTok"
msgstr ""

#: includes/core.php:1243
msgid "Mastodon"
msgstr ""

#: includes/core.php:1248
msgid "Bluesky"
msgstr ""

#. translators: %1$s is the title of the page/post. eg: Contact Menu.
#: includes/core.php:1354
#, php-format
msgid "%1$s Menu"
msgstr ""

#: includes/core.php:1400
msgid "That page can't be found"
msgstr ""

#. translators: Search query term(s).
#: includes/core.php:1418
#, php-format
msgid "Search for: %s"
msgstr ""

#: includes/core.php:1426
msgid "Nothing Found"
msgstr ""

#. translators: %s is the registered nav menu name
#: includes/customizer.php:108
#, php-format
msgid "Please assign a menu to the %s menu location"
msgstr ""

#: includes/customizer.php:292
msgid "Hide Site Title"
msgstr ""

#: includes/customizer.php:293
msgid "Prevent the site title from appearing in the header area."
msgstr ""

#: includes/customizer.php:313
msgid "Hide Tagline"
msgstr ""

#: includes/customizer.php:314
msgid "Prevent the site tagline from appearing in the header area."
msgstr ""

#: includes/customizer.php:348
msgid "Width"
msgstr ""

#: includes/customizer.php:377
msgid "Mobile Width"
msgstr ""

#: includes/customizer.php:403
msgid "Site Settings"
msgstr ""

#: includes/customizer.php:420
msgid "Page Titles"
msgstr ""

#: includes/customizer.php:421
msgid "Display page titles on individual pages that are not individually opted-out."
msgstr ""

#: includes/customizer.php:439
msgid "Blog Excerpt"
msgstr ""

#: includes/customizer.php:440
msgid "Use post excerpts on the blog page."
msgstr ""

#: includes/customizer.php:459
msgid "Copyright Text"
msgstr ""

#: includes/customizer.php:500
msgid "Design Style"
msgstr ""

#: includes/customizer.php:501
msgid "Choose a style, select a color scheme and customize colors to personalize your site."
msgstr ""

#: includes/customizer.php:524
msgid "Color scheme"
msgstr ""

#: includes/customizer.php:570
msgid "Secondary"
msgstr ""

#: includes/customizer.php:592
msgid "Tertiary"
msgstr ""

#: includes/customizer.php:614
msgid "Header Colors"
msgstr ""

#: includes/customizer.php:615
msgid "Customize colors within the site header."
msgstr ""

#: includes/customizer.php:635
#: includes/customizer.php:697
#: includes/customizer.php:856
#: includes/customizer.php:963
msgid "Background"
msgstr ""

#: includes/customizer.php:655
#: includes/customizer.php:717
#: includes/customizer.php:869
#: includes/customizer.php:976
msgid "Foreground"
msgstr ""

#: includes/customizer.php:676
msgid "Footer Colors"
msgstr ""

#: includes/customizer.php:677
msgid "Customize colors within the site footer."
msgstr ""

#: includes/customizer.php:737
#: includes/customizer.php:989
msgid "Heading"
msgstr ""

#: includes/customizer.php:757
msgid "Social Icon"
msgstr ""

#: includes/customizer.php:778
msgid "Additional Design Controls"
msgstr ""

#: includes/customizer.php:779
msgid "Customize additional design settings."
msgstr ""

#: includes/customizer.php:799
msgid "Site Spacing"
msgstr ""

#: includes/customizer.php:822
#: includes/customizer.php:841
msgid "Header"
msgstr ""

#: includes/customizer.php:842
msgid "Choose a header for every page on your site, then style it with the selectors below."
msgstr ""

#: includes/customizer.php:888
msgid "Sticky Header"
msgstr ""

#: includes/customizer.php:889
msgid "Set the header to sticky, so that it follows as users scroll."
msgstr ""

#: includes/customizer.php:908
msgid "Remove Search"
msgstr ""

#: includes/customizer.php:909
msgid "Remove the search icon from the header."
msgstr ""

#: includes/customizer.php:929
#: includes/customizer.php:948
msgid "Footer"
msgstr ""

#: includes/customizer.php:949
msgid "Choose a footer for every page on your site, then style it with the selectors below."
msgstr ""

#: includes/customizer.php:1009
msgid "Social"
msgstr ""

#: includes/customizer.php:1010
msgid "Add social media account links to apply social icons on the site footer."
msgstr ""

#: includes/customizer.php:1046
msgid "Social icon"
msgstr ""

#: includes/customizer.php:1066
msgid "Menu Behavior"
msgstr ""

#: includes/customizer.php:1083
msgid "Show sub menus on hover."
msgstr ""

#: includes/customizer.php:1084
msgid "Show sub menu items on hover."
msgstr ""

#: includes/customizer.php:1101
msgid "Site Design"
msgstr ""

#: includes/pluggable.php:28
msgid "Pingback:"
msgstr ""

#: includes/pluggable.php:28
msgid "(Edit)"
msgstr ""

#: includes/pluggable.php:44
#: includes/template-tags.php:114
msgid "Post author"
msgstr ""

#. translators: 1: date, 2: time
#: includes/pluggable.php:51
#, php-format
msgid "%1$s at %2$s"
msgstr ""

#: includes/pluggable.php:57
msgid "Your comment is awaiting moderation."
msgstr ""

#: includes/pluggable.php:62
msgid "Edit"
msgstr ""

#: includes/pluggable.php:71
msgid "Reply"
msgstr ""

#. Translators: %s = the author name.
#: includes/template-tags.php:120
#, php-format
msgctxt "%s = author name"
msgid "By %s"
msgstr ""

#: includes/template-tags.php:136
msgid "Post date"
msgstr ""

#: includes/template-tags.php:171
msgid "Categories"
msgstr ""

#: includes/template-tags.php:175
msgid "In"
msgstr ""

#: includes/template-tags.php:189
msgid "Tags"
msgstr ""

#: includes/template-tags.php:227
msgid "Featured"
msgstr ""

#. translators: %s: The social icon label.
#: includes/template-tags.php:754
#, php-format
msgid "Open %s in a new tab"
msgstr ""

#: includes/template-tags.php:921
msgid "Menu"
msgstr ""

#: includes/template-tags.php:961
msgid "Search Toggle"
msgstr ""

#: includes/title-meta.php:35
msgid "Hide Page Title."
msgstr ""

#: includes/woocommerce.php:150
msgid "View cart"
msgstr ""

#: includes/woocommerce.php:214
msgid "Close sidebar"
msgstr ""

#: includes/woocommerce.php:222
msgid "Cart"
msgstr ""

#. translators: 1. Single integer value. (eg: 1 product in your cart). 2. Integer larger than 1. (eg: 5 products in your cart).
#: includes/woocommerce.php:229
#: includes/woocommerce.php:279
#, php-format
msgid "%s product in your cart"
msgid_plural "%s products in your cart"
msgstr[0] ""
msgstr[1] ""

#: includes/woocommerce.php:332
msgid "Your cart is currently empty."
msgstr ""

#: includes/woocommerce.php:463
msgid "Previous Post: "
msgstr ""

#: includes/woocommerce.php:463
msgid "Previous"
msgstr ""

#: includes/woocommerce.php:464
msgid "Next Post:"
msgstr ""

#: includes/woocommerce.php:464
msgid "Next"
msgstr ""

#: includes/woocommerce.php:494
msgid "Back"
msgstr ""

#: includes/woocommerce.php:541
msgid "Reset Selections"
msgstr ""

#: partials/content-excerpt.php:25
msgctxt "post"
msgid "Featured"
msgstr ""

#. translators: 1: link to WP admin new post page.
#: partials/content-none.php:21
#, php-format
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr ""

#: partials/content-none.php:33
msgid "Sorry, but nothing matched your search terms. Please try searching again with some different keywords."
msgstr ""

#: partials/content-none.php:38
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help you out."
msgstr ""

#: partials/content.php:44
msgid "Page"
msgstr ""

#: partials/content.php:44
msgid "Pages:"
msgstr ""

#: partials/footers/footer-1.php:18
#: partials/footers/footer-2.php:21
msgid "Footer Menu"
msgstr ""

#: partials/footers/footer-3.php:21
#: partials/footers/footer-4.php:21
msgid "Primary Footer Menu"
msgstr ""

#: partials/footers/footer-3.php:36
#: partials/footers/footer-4.php:37
msgid "Secondary Footer Menu"
msgstr ""

#: partials/footers/footer-3.php:51
#: partials/footers/footer-4.php:53
msgid "Tertiary Footer Menu"
msgstr ""

#: partials/layouts/about.php:16
#: partials/layouts/about.php:130
msgid "About"
msgstr ""

#: partials/layouts/about.php:22
msgid "Hi, I’m Everett"
msgstr ""

#: partials/layouts/about.php:31
msgid "A tenacious, loving and energetic photographer who enjoys grabbing her camera and running out to take some photos."
msgstr ""

#: partials/layouts/about.php:39
msgid "Work With Me"
msgstr ""

#: partials/layouts/about.php:54
#: partials/layouts/about.php:61
#: partials/layouts/about.php:68
#: partials/layouts/about.php:219
#: partials/layouts/contact.php:104
#: partials/layouts/contact.php:134
#: partials/layouts/contact.php:164
#: partials/layouts/contact.php:303
#: partials/layouts/contact.php:310
#: partials/layouts/gallery.php:79
#: partials/layouts/gallery.php:104
#: partials/layouts/gallery.php:111
#: partials/layouts/gallery.php:121
#: partials/layouts/gallery.php:149
#: partials/layouts/gallery.php:154
#: partials/layouts/gallery.php:159
#: partials/layouts/gallery.php:164
#: partials/layouts/gallery.php:169
#: partials/layouts/gallery.php:174
#: partials/layouts/gallery.php:179
#: partials/layouts/gallery.php:184
#: partials/layouts/gallery.php:189
#: partials/layouts/gallery.php:194
#: partials/layouts/gallery.php:199
#: partials/layouts/gallery.php:204
#: partials/layouts/gallery.php:209
#: partials/layouts/gallery.php:214
#: partials/layouts/gallery.php:219
#: partials/layouts/home.php:50
#: partials/layouts/home.php:139
#: partials/layouts/home.php:175
#: partials/layouts/home.php:219
#: partials/layouts/home.php:272
#: partials/layouts/home.php:279
#: partials/layouts/home.php:374
#: partials/layouts/home.php:400
#: partials/layouts/home.php:512
#: partials/layouts/home.php:542
#: partials/layouts/home.php:573
#: partials/layouts/home.php:766
#: partials/layouts/home.php:773
#: partials/layouts/home.php:780
#: partials/layouts/home.php:787
#: partials/layouts/home.php:794
#: partials/layouts/home.php:803
#: partials/layouts/home.php:821
#: partials/layouts/home.php:828
#: partials/layouts/home.php:835
#: partials/layouts/home.php:885
#: partials/layouts/home.php:904
#: partials/layouts/home.php:911
#: partials/layouts/home.php:918
#: partials/layouts/home.php:1206
msgid "Image description"
msgstr ""

#: partials/layouts/about.php:86
msgid "Early on"
msgstr ""

#: partials/layouts/about.php:94
msgid "I am so fascinated by photography and it’s capability to bring your imagination to amazing places. Early on, I fell in love with the idea of filming my own productions, so I set out to learn everything I could."
msgstr ""

#: partials/layouts/about.php:108
msgid "Current"
msgstr ""

#: partials/layouts/about.php:116
msgid "I have been teaching myself filmmaking for the past four and a half years and I’m still learning every day. I am building my business as a freelance filmmaker, as well as working on my own photo shoots."
msgstr ""

#: partials/layouts/about.php:143
msgid "Protecting yourself"
msgstr ""

#: partials/layouts/about.php:153
msgid "Miller &amp; Cole is tremendously proud of the impact that we have made in helping our clients by providing quality legal services and exceptional service."
msgstr ""

#: partials/layouts/about.php:175
msgid "Quality Results"
msgstr ""

#: partials/layouts/about.php:183
msgid "Our goal is to create assets from our clients’ innovations through patent, trademark and copyright law.&nbsp; We take great pride in providing quality trademark legal services and exceptional customer service every single day."
msgstr ""

#: partials/layouts/about.php:198
msgid "Experienced"
msgstr ""

#: partials/layouts/about.php:206
msgid "The attorneys at Miller &amp; Cole work as a team to exceed each of our clients’ expectations. We have 30+ years of high-level experience helping businesses protecting the time, money and resources spent developing ideas and inventions."
msgstr ""

#: partials/layouts/about.php:241
#: partials/layouts/contact.php:16
#: partials/layouts/contact.php:194
#: partials/layouts/contact.php:208
#: partials/layouts/home.php:1192
msgid "Contact"
msgstr ""

#: partials/layouts/about.php:250
msgid "Office => (555) 555-5555<br><EMAIL>"
msgstr ""

#: partials/layouts/about.php:265
msgid "Location"
msgstr ""

#: partials/layouts/about.php:274
msgid "123 Example Rd<br>Scottsdale, AZ 85260"
msgstr ""

#: partials/layouts/about.php:289
msgid "Connect"
msgstr ""

#: partials/layouts/about.php:298
msgid "<a href=\"https://twitter.com\">Twitter</a><br><a href=\"https://www.facebook.com\">Facebook</a><br>"
msgstr ""

#: partials/layouts/contact.php:22
msgid "Let's get in touch"
msgstr ""

#: partials/layouts/contact.php:31
msgid "Well hello there, wonderful, fabulous&nbsp;you!&nbsp;If you’d like to get in touch with me, please feel free to give me a call at (555) 555-5555, or send a message with the form down below. Either way, I'll be in touch shortly!"
msgstr ""

#: partials/layouts/contact.php:80
#: partials/layouts/contact.php:287
msgid "Contact Us"
msgstr ""

#: partials/layouts/contact.php:112
msgid "\"I appreciate Everett's ability to compose visually stunning photos, brining my memories to live every time I look at them.\""
msgstr ""

#: partials/layouts/contact.php:121
msgid "- Larina H."
msgstr ""

#: partials/layouts/contact.php:142
msgid "\"Everett should be nominated for photographer of the year. I am so pleased with her photography at my wedding.\""
msgstr ""

#: partials/layouts/contact.php:151
msgid "- Kam V."
msgstr ""

#: partials/layouts/contact.php:172
msgid "\"Everett knew exactly how to pull the best of me out, and into a beautiful portrait. I'm so glad I met Everett!\""
msgstr ""

#: partials/layouts/contact.php:181
msgid "- Jerri S."
msgstr ""

#: partials/layouts/contact.php:217
msgid "Studio Gym<br>123 Example Rd, Scottsdale, AZ 85260<br>(555) 555-5555"
msgstr ""

#: partials/layouts/contact.php:232
msgid "Hours"
msgstr ""

#: partials/layouts/contact.php:241
msgid "Mon-Fri => 8:00 - 21:00<br>Sat => 8:00 - 20:00<br>Sun => 10:00 - 14:00"
msgstr ""

#: partials/layouts/gallery.php:16
#: partials/layouts/gallery.php:41
#: partials/layouts/gallery.php:141
msgid "Gallery"
msgstr ""

#: partials/layouts/gallery.php:54
#: partials/layouts/home.php:102
msgid "Connecting audience + artist in our lush, speakeasy-style listening room. Only 50 seats available for this sought-after scene."
msgstr ""

#: partials/layouts/home.php:16
#: partials/layouts/home.php:212
#: partials/layouts/home.php:394
#: partials/layouts/home.php:720
#: partials/layouts/home.php:879
msgid "Homepage"
msgstr ""

#: partials/layouts/home.php:29
msgid "Where the hustle slows, the rhythm is heard, and the beans are fantastic"
msgstr ""

#: partials/layouts/home.php:89
msgid "Enjoy Live Music + the Best Coffee You've Ever Had"
msgstr ""

#: partials/layouts/home.php:146
msgid "A social house"
msgstr ""

#: partials/layouts/home.php:158
msgid "With our guides' experience, we will not only get you to where the fish are - but we'll get you hooked on them too. Our crew is knowledgeable and friendly - ready to take you on the trip of your dreams."
msgstr ""

#: partials/layouts/home.php:182
msgid "A listening room"
msgstr ""

#: partials/layouts/home.php:194
msgid "Folks have fought some monster bluefin tuna on standup gear with our offshore fishing packager, which is an incredible challenge for sure! Stick to the shoreline and test your strength pulling in some biggies!"
msgstr ""

#: partials/layouts/home.php:238
msgid "Our approach reflects the people we serve. We are diverse, yet the same."
msgstr ""

#: partials/layouts/home.php:251
msgid "Learn More"
msgstr ""

#: partials/layouts/home.php:319
msgid "When we set up shop with an espresso machine up front and a roaster in the back, we hoped to some day be a part of New York's rich tradition of service and culinary achievement. Everyday this aspiration drives us."
msgstr ""

#: partials/layouts/home.php:331
msgid "The city's energy binds us together. It drives us to be the best."
msgstr ""

#: partials/layouts/home.php:343
msgid "This fairly new coffee shop, conveniently located in downtown Scottsdale, is one of the best coffee shops I've ever been to, and trust me when I say, I've been to many. The owners and the staff will make you feel like an old friend or even family."
msgstr ""

#: partials/layouts/home.php:356
msgid "Grab a cup"
msgstr ""

#: partials/layouts/home.php:412
msgid "Bringing the finest culinary food from the heart of Asia directly to you"
msgstr ""

#: partials/layouts/home.php:421
msgid "View our Menu"
msgstr ""

#: partials/layouts/home.php:459
msgid "<strong>Sushi Nakazawa</strong>&nbsp;serves the&nbsp;<em>omakase</em>&nbsp;of&nbsp;<strong>Chef Daisuke Nakazawa</strong>. Within the twenty-course meal lies Chef Nakazawa’s passion for sushi. With ingredients sourced both domestically and internationally, the chef crafts a very special tasting menu within the style of Edomae sushi. Chef Nakazawa is a strong believer in the food he serves representing the waters he is surrounded by, so only the best and freshest find its way to your plate."
msgstr ""

#: partials/layouts/home.php:467
msgid "The relaxed dining experience at Sushi Nakazawa is chic nonetheless. High back leather chairs at the sushi bar coddle you while each course is explained in detail, and every nuance is revealed. Whether an Edomae novice or self-proclaimed sushi foodie, you will leave with a feeling of euphoria."
msgstr ""

#: partials/layouts/home.php:520
msgid "Authentic"
msgstr ""

#: partials/layouts/home.php:529
msgid "The relaxed dining experience at Bento is chic and airy. High back chairs at the sushi bar coddle you for each course."
msgstr ""

#: partials/layouts/home.php:550
msgid "Historical"
msgstr ""

#: partials/layouts/home.php:552
#: partials/layouts/home.php:583
msgid "Write title..."
msgstr ""

#: partials/layouts/home.php:560
msgid "Housed in the original Yami House, the history behind Bento is amazing. Learn more as you dine in our historical dining room."
msgstr ""

#: partials/layouts/home.php:581
msgid "Best-rated"
msgstr ""

#: partials/layouts/home.php:591
msgid "Bento is one of the best-rated restaurants in the region. With glamourous food and delicious drinks - you won't want to miss out!"
msgstr ""

#: partials/layouts/home.php:651
msgid "Bento, Steak &amp; Sushi"
msgstr ""

#: partials/layouts/home.php:660
msgid "123 Example Rd"
msgstr ""

#: partials/layouts/home.php:669
msgid "Scottsdale, AZ 85260"
msgstr ""

#: partials/layouts/home.php:677
msgid "Reservations"
msgstr ""

#: partials/layouts/home.php:732
msgid "Hello! We're a"
msgstr ""

#: partials/layouts/home.php:741
msgid "Branding &amp; Digital Design<br>Studio in Tokyo"
msgstr ""

#: partials/layouts/home.php:749
#: partials/layouts/home.php:867
msgid "Let's Talk"
msgstr ""

#: partials/layouts/home.php:850
msgid "Need our help?"
msgstr ""

#: partials/layouts/home.php:859
msgid "We Create Brands and Inspire Experiences"
msgstr ""

#: partials/layouts/home.php:927
msgid "Welcome to Salt"
msgstr ""

#: partials/layouts/home.php:936
msgid "For over forty years Salt has been known for its luxury lobster, steamed clams, barbecued chicken and homemade clam chowder. Stop on by and grab some of the most incredible seafood you'll ever taste."
msgstr ""

#: partials/layouts/home.php:952
msgid "Food &amp; Hours"
msgstr ""

#: partials/layouts/home.php:1034
msgid "Ocean to Plate"
msgstr ""

#: partials/layouts/home.php:1036
#: partials/layouts/home.php:1106
#: partials/layouts/home.php:1176
msgid "Add feature title..."
msgstr ""

#: partials/layouts/home.php:1043
msgid "Cajun &amp; creole seafood cuisine, never frozen - delish"
msgstr ""

#: partials/layouts/home.php:1051
msgid "See Menu"
msgstr ""

#: partials/layouts/home.php:1104
msgid "Dine with Us"
msgstr ""

#: partials/layouts/home.php:1113
msgid "Mon - Thurs => 5pm - 11pm<br> Fri-Sat => 5pm - Midnight"
msgstr ""

#: partials/layouts/home.php:1121
msgid "Dine in"
msgstr ""

#: partials/layouts/home.php:1174
msgid "Catering"
msgstr ""

#: partials/layouts/home.php:1183
msgid "We cater all events from family reunions, to weddings"
msgstr ""

#: partials/layouts/home.php:1185
msgid "Add feature content"
msgstr ""

#: partials/pagination.php:21
msgid "Newer <span class=\"nav-short\">Posts</span>"
msgstr ""

#: partials/pagination.php:25
msgid "Older <span class=\"nav-short\">Posts</span>"
msgstr ""

#: searchform.php:21
msgctxt "label"
msgid "Search for:"
msgstr ""

#: searchform.php:23
msgctxt "placeholder"
msgid "Search &hellip;"
msgstr ""

#: searchform.php:25
msgctxt "submit button"
msgid "Submit"
msgstr ""

#. translators: 1. Single integer value. (eg: Items (1), Items (2) etc.).
#: woocommerce/cart/cart.php:32
#, php-format
msgid "Items (%s)"
msgstr ""

#: woocommerce/cart/cart.php:89
msgid "Remove"
msgstr ""
